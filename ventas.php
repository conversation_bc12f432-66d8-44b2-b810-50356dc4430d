<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';

// Añade estos headers al inicio de tu archivo
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado
require_once 'db_connection.php';



error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventas - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/table.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/header.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/inventory.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/dashboard.css?v=<?php echo time(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>

    html {
            zoom: 82%;
        }

         /* Ajustes específicos para la tabla DTE */
    #dteDatatable {
        font-size: 0.75rem !important; /* Reducción más agresiva del tamaño de fuente */
    }

    #dteDatatable td,
    #dteDatatable th {
        padding: 0.3rem 0.5rem !important; /* Reduce el padding vertical y horizontal */
        line-height: 1.2 !important; /* Reduce el espaciado entre líneas */
    }

    #dteDatatable .badge {
        font-size: 0.7rem !important;
        padding: 0.2em 0.4em !important;
    }

    #dteDatatable .btn-sm {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.75rem !important;
    }

    /* Mantener encabezados legibles */
    #dteDatatable thead th {
        font-size: 0.8rem !important;
        font-weight: 600 !important;
    }

    /* Ajuste para el contenedor de la tabla */
    .table-responsive {
        font-size: 0.75rem !important;
    }

        .pdf-container {
            position: sticky;
            top: 20px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #pdf-placeholder {
            color: #6c757d;
        }

        .pdf-container iframe {
            border-radius: 4px;
        }

    /* Estilos para el buscador */
    #dteBuscador {
        border-radius: 0 4px 4px 0;
        border-left: 0;
    }

    #dteBuscador:focus {
        box-shadow: none;
        border-color: #ced4da;
    }

    .input-group-text {
        background-color: #fff;
        border-right: 0;
        border-radius: 4px 0 0 4px;
    }

    /* Ajuste para mantener consistencia con el diseño compacto */
    .input-group {
        max-width: 300px;
    }
    
    /* Estilos para los botones de iconos */
    .icon-button {
        background: none;
        border: none;
        padding: 0;
        color: inherit;
        line-height: 1;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;
    }

    .icon-button i {
        margin: 0;
    }

    /* Estilos para los contadores */
    .dte-count, .quote-count {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #e74c3c;
        color: white;
        border-radius: 50%;
        padding: 3px 7px;
        font-size: 0.8rem;
        font-weight: bold;
        min-width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        z-index: 1;
        border: 1px solid white;
        pointer-events: none;
    }
    
    .quote-count {
        background-color: #3498db;
    }

    /* Estilos para el menú desplegable de usuario */
    .user-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 10px;
        min-width: 200px;
        z-index: 1000;
        display: none;
    }

    .icon-container > i.fa-user {
        cursor: pointer;
        position: relative;
    }

    .icon-container > i.fa-user:hover + .user-dropdown,
    .user-dropdown:hover {
        display: block;
    }

    .user-info {
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .user-name {
        display: block;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .user-role {
        display: block;
        font-size: 0.8rem;
        color: #7f8c8d;
        text-transform: capitalize;
    }

    .logout-btn {
        display: flex;
        align-items: center;
        color: #e74c3c;
        text-decoration: none;
        padding: 5px 0;
        font-size: 0.9rem;
    }

    .logout-btn i {
        margin-right: 5px;
    }
    </style>
</head>

<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                Tata repuestos
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <!-- Icono de Notificación -->
                    <i class="fas fa-bell"></i>

                    <!-- BOTÓN COTIZACIÓN ESTILIZADO COMO ICONO -->
                    <button id="openQuoteBtn" class="icon-button" title="Abrir Cotización">
                         <i class="fas fa-file-invoice-dollar"></i>
                         <span class="quote-count">0</span>
                    </button>

                    <!-- BOTÓN DTE ESTILIZADO COMO ICONO (MANTENIENDO EL ID) -->
                    <button id="openDTEBtn" class="icon-button" title="Abrir DTE">
                         <i class="fas fa-file-invoice"></i>
                         <span class="dte-count">0</span>
                    </button>
                    
                    <!-- Icono de Usuario -->
                    <i class="fas fa-user"></i>
                    <div class="user-dropdown">
                        <div class="user-info">
                            <span class="user-name"><?php echo htmlspecialchars($_SESSION['nombre'] ?? 'Usuario'); ?></span>
                            <span class="user-role"><?php echo htmlspecialchars($_SESSION['rol'] ?? 'Sin rol'); ?></span>
                        </div>
                        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid mt-4">
        <!-- Tabs de navegación -->
        <ul class="nav nav-tabs mb-4" id="ventasTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="resumen-tab" data-bs-toggle="tab" data-bs-target="#resumen" type="button" role="tab">
                    <i class="fas fa-chart-pie"></i> Resumen
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dte-tab" data-bs-toggle="tab" data-bs-target="#dte" type="button" role="tab">
                    <i class="fas fa-file-invoice"></i> DTE
                </button>
            </li>

        </ul>

        <!-- Contenido de los tabs -->
        <div class="tab-content" id="ventasTabContent">
            <!-- Tab DTE -->
            <div class="tab-pane fade" id="dte" role="tabpanel">
                <div class="row">
                    <!-- Tabla DTE -->
                    <div class="col-md-8">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-invoice"></i> Documentos Tributarios Electrónicos (DTE)
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <!-- Agregar el selector de meses -->
                                <div class="p-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="mes-selector" class="form-label">Filtrar por mes:</label>
                                            <div class="input-group">
                                                <select id="mes-selector" class="form-select">
                                                    <option value="current" selected>Últimos 30 registros</option>
                                                <?php
                                                // Generar opciones de meses desde Abril 2025 hasta el mes actual
                                                $currentYear = date('Y');
                                                $currentMonth = date('n');
                                                $startYear = 2025;
                                                $startMonth = 4; // Abril
                                                
                                                for ($year = $startYear; $year <= $currentYear; $year++) {
                                                    $endMonth = ($year == $currentYear) ? $currentMonth : 12;
                                                    $beginMonth = ($year == $startYear) ? $startMonth : 1;
                                                    
                                                    for ($month = $beginMonth; $month <= $endMonth; $month++) {
                                                        $monthPadded = str_pad($month, 2, '0', STR_PAD_LEFT);
                                                        $monthName = date('F', mktime(0, 0, 0, $month, 1));
                                                        $monthName = ucfirst(strftime('%B', mktime(0, 0, 0, $month, 1)));
                                                        // Quitamos el selected de las opciones de mes ya que queremos que 'Últimos 30 registros' esté seleccionado por defecto
                                                        echo "<option value='$year-$monthPadded'>$monthName $year</option>";
                                                    }
                                                }
                                                ?>
                                                </select>
                                                <button id="export-xls-btn" class="btn btn-success" title="Exportar a Excel">
                                                    <i class="fas fa-file-excel"></i> Exportar
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6 d-flex align-items-end justify-content-end">
                                            <div id="loading-indicator" style="display: none;" class="text-primary">
                                                <i class="fas fa-spinner fa-spin me-1"></i> Cargando datos...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="loading-overlay" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255,255,255,0.8); z-index: 100; justify-content: center; align-items: center;">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">Cargando datos...</p>
                                    </div>
                                </div>
                                <div class="table-responsive position-relative" style="height: calc(100vh - 300px); overflow-y: auto;">
                                    <table id="tabla_repuestos" class="table table-striped table-hover sortable-table mb-0">
                                        <thead>
                                            <tr>
                                                <th>Acciones</th>
                                                <th class="sortable" data-column="1" data-sort="string">Tipo DTE <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="2" data-sort="number">Folio <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="3" data-sort="date">Fecha Generación <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="4" data-sort="string">RUT Receptor <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="5" data-sort="string">Razón Social <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="6" data-sort="number">Monto Total <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="7" data-sort="string">Estado Sobre <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="8" data-sort="number">ID Sobre <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="9" data-sort="string">Track ID <i class="fas fa-sort"></i></th>
                                                <th class="sortable" data-column="10" data-sort="string">ID Voucher <i class="fas fa-sort"></i></th>
                                            </tr>
                                            <tr>
                                                <th></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="1" placeholder="Filtrar..." id="filter-tipo"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="2" placeholder="Filtrar..." id="filter-folio"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="3" placeholder="Filtrar..." id="filter-fecha"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="4" placeholder="Filtrar..." id="filter-rut"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="5" placeholder="Filtrar..." id="filter-razon"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="6" placeholder="Filtrar..." id="filter-monto"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="7" placeholder="Filtrar..." id="filter-estado"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="8" placeholder="Filtrar..." id="filter-sobre"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="9" placeholder="Filtrar..." id="filter-track"></th>
                                                <th><input type="text" class="form-control form-control-sm column-search" data-column="10" placeholder="Filtrar..." id="filter-voucher"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            try {
                                                $conn = getConnection();
                                                $stmt = $conn->prepare("
                                                    SELECT DTE.*
                                                    , ENVIOS.estado_envio
                                                    , ENVIOS.glosa
                                                    , ENVIOS.trackid
                                                    , ENVIOS.fecha_envio
                                                    , COALESCE(JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Totales.MontoTotal')), 0) as monto_total
                                                    , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.Rut')) as rut_receptor
                                                    , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.RazonSocial')) as razon_social_receptor
                                                    , DTE.archivo_pdf
                                                    , DTE.id_voucher
                                                    FROM tb_facturas_dte DTE
                                                    LEFT JOIN tb_sobre_envios ENVIOS
                                                    ON DTE.id_sobre = ENVIOS.id
                                                    ORDER BY fecha_generacion DESC
                                                    LIMIT 30
                                                ");
                                                $stmt->execute();
                                                $documentos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                                foreach ($documentos as $doc) {
                                                    echo "<tr data-dte-id='" . intval($doc['id']) . "'>";
                                                    echo "<td>";
                                                    echo '<div class="btn-group" role="group">';
                                                    echo '<button class="btn btn-info btn-sm me-1" onclick="viewPDF(\'' . htmlspecialchars($doc['archivo_pdf'] ?? '') . '\')" title="Ver PDF"><i class="fas fa-file-pdf"></i></button>';
                                                    echo '<a href="' . htmlspecialchars($doc['nombre_archivo'] ?? '') . '" class="btn btn-success btn-sm me-1" download title="Descargar XML"><i class="fas fa-download"></i></a>';
                                                    echo '<button class="btn btn-warning btn-sm" onclick="openVoucherModal(' . intval($doc['id']) . ', \'' . htmlspecialchars($doc['id_voucher'] ?? '') . '\')" title="Actualizar ID Voucher"><i class="fas fa-receipt"></i></button>';
                                                    echo '</div>';
                                                    echo "</td>";
                                                    echo "<td>";
                                                    if ($doc['tipo_dte'] == 33) {
                                                        echo "<span class='badge bg-primary'>Factura (33)</span>";
                                                    } elseif ($doc['tipo_dte'] == 39) {
                                                        echo "<span class='badge bg-success'>Boleta (39)</span>";
                                                    } elseif ($doc['tipo_dte'] == 61) {
                                                        echo "<span class='badge bg-info'>Nota Crédito (61)</span>";
                                                    } elseif ($doc['tipo_dte'] == 56) {
                                                        echo "<span class='badge bg-warning'>Nota Débito (56)</span>";
                                                    }
                                                    echo "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['folio'] ?? '') . "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['fecha_generacion'] ?? '') . "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['rut_receptor'] ?? '') . "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['razon_social_receptor'] ?? '') . "</td>";
                                                    echo "<td>$" . (is_null($doc['monto_total']) ? '0' : number_format($doc['monto_total'], 0, ',', '.')) . "</td>";
                                                    echo "<td>";
                                                    if ($doc['estado_sobre'] == 1) {
                                                        echo '<span class="badge bg-success">Generado</span>';
                                                    } elseif ($doc['estado_sobre'] == 0) {
                                                        echo '<span class="badge bg-warning">Pendiente</span>';
                                                    }
                                                    echo "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['id_sobre'] ?? '') . "</td>";
                                                    echo "<td>" . htmlspecialchars($doc['trackid'] ?? 'N/A') . "</td>";
                                                    echo "<td class='voucher-cell'>" . htmlspecialchars($doc['id_voucher'] ?? 'N/A') . "</td>";
                                                    echo "</tr>";
                                                }
                                            } catch(Exception $e) {
                                                echo "<tr><td colspan='11'>Error al cargar los documentos DTE: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Visor de PDF -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-pdf"></i> Visor de Documentos
                                </h5>
                            </div>
                            <div class="card-body p-0" style="height: calc(100vh - 250px); overflow-y: auto;">
                                <div id="pdf-viewer" class="pdf-container h-100">
                                    <div id="pdf-placeholder" class="text-center p-5">
                                        <i class="fas fa-file-pdf fa-3x text-muted"></i>
                                        <p class="mt-3">Seleccione un documento para visualizar</p>
                                    </div>
                                    <iframe id="pdf-frame" style="display: none; width: 100%; height: 100%; border: none;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Resumen -->
            <div class="tab-pane fade show active" id="resumen" role="tabpanel">
                <div class="dashboard-container" id="dashboard-container">
                    <!-- Título del período con selector integrado -->
                    <div class="row mb-4">
                        <div class="col-12 d-flex justify-content-between align-items-center">
                            <div class="dashboard-title">
                                <i class="fas fa-chart-line"></i>
                                <span>Dashboard de Ventas - <span id="titulo-periodo">Este Mes</span></span>
                            </div>
                            <div class="periodo-selector-container">
                                <select id="periodo-selector" class="periodo-selector">
                                    <option value="dia">Hoy</option>
                                    <option value="semana">Últimos 7 días</option>
                                    <option value="mes" selected>Este Mes</option>
                                    <option value="anio">Este Año</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- KPIs Principales -->
                    <div class="row mb-4">
                        <div class="col-md-4 mb-3">
                            <div class="kpi-card">
                                <div class="kpi-title">Venta Total</div>
                                <div class="kpi-value" id="kpi-venta-total">$0</div>
                                <div class="variacion positiva" id="kpi-venta-variacion">+0%</div>
                                <div class="kpi-subtitle">vs período anterior</div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="kpi-card">
                                <div class="kpi-title">Número de Transacciones</div>
                                <div class="kpi-value" id="kpi-num-transacciones">0</div>
                                <div class="variacion positiva" id="kpi-transacciones-variacion">+0%</div>
                                <div class="kpi-subtitle">vs período anterior</div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="kpi-card">
                                <div class="kpi-title">Ticket Promedio</div>
                                <div class="kpi-value" id="kpi-ticket-promedio">$0</div>
                                <div class="kpi-subtitle">por transacción</div>
                            </div>
                        </div>
                    </div>



                    <!-- Gráficos de Tendencias -->
                    <div class="row mb-4">
                        <!-- Distribución por Tipo de DTE (Primer elemento) -->
                        <div class="col-md-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-title">
                                    <span id="titulo-distribucion-dte">Distribución por Tipo de DTE</span>
                                </div>
                                <div class="chart-container">
                                    <canvas id="grafico-distribucion-dte"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-title">
                                    <span id="titulo-ventas-diarias">Ventas Diarias del Mes</span>
                                </div>
                                <div class="chart-container">
                                    <canvas id="grafico-ventas-diarias"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-title">Comparativa Mensual</div>
                                <div class="chart-container">
                                    <canvas id="grafico-comparativa-mensual"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-title">Tendencia Anual y Proyección</div>
                                <div class="chart-container">
                                    <canvas id="grafico-tendencia-anual"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-title">Estacionalidad (Ventas por Día)</div>
                                <div class="chart-container">
                                    <canvas id="grafico-estacionalidad"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Espacio vacío o para futuro gráfico -->
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <style>
        .card {
            margin-bottom: 0;
        }

        .table-responsive {
            border-radius: 0 0 0.25rem 0.25rem;
        }

        #dteDatatable {
            margin-bottom: 0;
        }

        .pdf-container {
            background: white;
            border-radius: 0 0 0.25rem 0.25rem;
        }

        #pdf-placeholder {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
    </style>

    <style>
        .sortable-table th {
            cursor: pointer;
            user-select: none;
        }

        .sortable-table th i {
            margin-left: 5px;
            opacity: 0.3;
        }

        .sortable-table th.sort-asc i:before {
            content: "\f0de";
            opacity: 1;
        }

        .sortable-table th.sort-desc i:before {
            content: "\f0dd";
            opacity: 1;
        }

        /* Estilos para los filtros de columna */
        .column-search {
            font-size: 0.7rem !important;
            padding: 0.2rem 0.4rem !important;
            height: auto !important;
        }

        /* Mensaje de no resultados */
        .no-results td {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            font-style: italic;
        }
    </style>

    <script src="js/simple-table.js?v=<?php echo time(); ?>"></script>
    <script>
    // Funciones para el visor de PDF y regeneración de XML
    function regenerarXML(id) {
        if (confirm('¿Está seguro que desea regenerar el XML?')) {
            fetch('regenerar_xml.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('XML regenerado exitosamente');
                    location.reload();
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                alert('Error al regenerar XML: ' + error.message);
            });
        }
    }

    function downloadXML(xmlFileName) {
        if (xmlFileName) {
            window.location.href = 'download.php?file=' + encodeURIComponent(xmlFileName) + '&type=xml';
        } else {
            alert('Archivo XML no disponible');
        }
    }
    </script>
    <script>
        async function reprocesarDTE(id) {
            if (confirm('¿Está seguro que desea reprocesar este DTE?')) {
                try {
                    const response = await fetch('reprocesar_dte.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ id: id })
                    });

                    const result = await response.json();
                    if (result.success) {
                        alert('DTE reprocesado exitosamente');
                        location.reload();
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    alert('Error al reprocesar DTE: ' + error.message);
                }
            }
        }
    </script>
    <script>
        function viewPDF(pdfPath) {
            const pdfFrame = document.getElementById('pdf-frame');
            const pdfPlaceholder = document.getElementById('pdf-placeholder');

            if (pdfPath) {
                // Mostrar el iframe y ocultar el placeholder
                pdfFrame.style.display = 'block';
                pdfPlaceholder.style.display = 'none';

                // Usar download.php para mostrar el PDF de forma segura
                pdfFrame.src = `download.php?file=${encodeURIComponent(pdfPath)}&view=true`;

                // Hacer scroll suave en dispositivos móviles
                if (window.innerWidth < 768) {
                    document.getElementById('pdf-viewer').scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            }
        }

        // Función para manejar la respuesta adaptativa
        function handleResponsiveView() {
            const pdfViewer = document.getElementById('pdf-viewer');
            if (window.innerWidth < 768) {
                pdfViewer.style.height = '500px'; // Altura más pequeña en móviles
            } else {
                pdfViewer.style.height = '800px'; // Altura original en desktop
            }
        }

        // Escuchar cambios en el tamaño de la ventana
        window.addEventListener('resize', handleResponsiveView);
        // Ejecutar al cargar la página
        handleResponsiveView();
        
        // Función para cargar datos iniciales
        function loadInitialData() {
            // Asegurarse de que 'Últimos 30 registros' esté seleccionado
            const mesSelector = document.getElementById('mes-selector');
            mesSelector.value = 'current';
            
            // Cargar los datos iniciales solo si no se han cargado ya
            if (typeof loadDTEData === 'function' && !window.dataLoaded) {
                loadDTEData('current');
                window.dataLoaded = true;
            }
        }
        
        // Cargar datos cuando el DOM esté listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadInitialData);
        } else {
            loadInitialData();
        }
    </script>
    <script>
        // Agregar manejo de tabs
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar los tabs de Bootstrap
            var triggerTabList = [].slice.call(document.querySelectorAll('#ventasTab button'))
            triggerTabList.forEach(function(triggerEl) {
                var tabTrigger = new bootstrap.Tab(triggerEl)
                triggerEl.addEventListener('click', function(event) {
                    event.preventDefault()
                    tabTrigger.show()
                })
            })
            
            // Utilizamos una variable para evitar cargas duplicadas
            if (typeof window.initialDataLoaded === 'undefined') {
                window.initialDataLoaded = true;
            }
            
            // Manejar el cambio del selector de meses
            const mesSelector = document.getElementById('mes-selector');
            const loadingIndicator = document.getElementById('loading-indicator');
            const tableBody = document.querySelector('#tabla_repuestos tbody');
            const exportXlsBtn = document.getElementById('export-xls-btn');
            const filterInputs = document.querySelectorAll('.column-search');
            
            // Función para limpiar todos los filtros
            function clearAllFilters() {
                filterInputs.forEach(input => {
                    input.value = '';
                });
            }
            
            mesSelector.addEventListener('change', function() {
                const selectedMonth = this.value;
                // Limpiar todos los filtros antes de cargar nuevos datos
                clearAllFilters();
                loadDTEData(selectedMonth);
            });
            
            // Manejar el clic en el botón de exportar a Excel
            exportXlsBtn.addEventListener('click', function() {
                const selectedMonth = mesSelector.value;
                window.location.href = 'export_dte_data.php?mes=' + selectedMonth;
            });
            
            function loadDTEData(mes) {
                // Mostrar indicador de carga
                loadingIndicator.style.display = 'block';
                document.getElementById('loading-overlay').style.display = 'flex';
                
                // Realizar petición AJAX
                fetch(`get_dte_data.php?mes=${mes}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateTable(data.data);
                        } else {
                            alert('Error al cargar los datos: ' + data.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error al cargar los datos');
                    })
                    .finally(() => {
                        // Ocultar indicador de carga
                        loadingIndicator.style.display = 'none';
                        document.getElementById('loading-overlay').style.display = 'none';
                    });
            }
            
            function updateTable(documentos) {
                // Limpiar el tbody
                tableBody.innerHTML = '';
                
                // Limpiar los filtros cuando se actualiza la tabla
                clearAllFilters();
                
                if (documentos.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="11" class="text-center">No se encontraron documentos para el período seleccionado</td></tr>';
                    return;
                }
                
                // Agregar las filas
                documentos.forEach(doc => {
                    let tipoDteBadge = '';
                    switch(parseInt(doc.tipo_dte)) {
                        case 33:
                            tipoDteBadge = '<span class="badge bg-primary">Factura (33)</span>';
                            break;
                        case 39:
                            tipoDteBadge = '<span class="badge bg-success">Boleta (39)</span>';
                            break;
                        case 61:
                            tipoDteBadge = '<span class="badge bg-info">Nota Crédito (61)</span>';
                            break;
                        case 56:
                            tipoDteBadge = '<span class="badge bg-warning">Nota Débito (56)</span>';
                            break;
                    }
                    
                    let estadoSobreBadge = '';
                    if (doc.estado_sobre == 1) {
                        estadoSobreBadge = '<span class="badge bg-success">Generado</span>';
                    } else if (doc.estado_sobre == 0) {
                        estadoSobreBadge = '<span class="badge bg-warning">Pendiente</span>';
                    }
                    
                    const montoFormateado = doc.monto_total === null ? '$0' : '$' + parseInt(doc.monto_total).toLocaleString('es-CL');
                    
                    const row = `
                        <tr data-dte-id="${doc.id}">
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-info btn-sm me-1" onclick="viewPDF('${doc.archivo_pdf || ''}')" title="Ver PDF"><i class="fas fa-file-pdf"></i></button>
                                    <a href="${doc.nombre_archivo || ''}" class="btn btn-success btn-sm me-1" download title="Descargar XML"><i class="fas fa-download"></i></a>
                                    <button class="btn btn-warning btn-sm" onclick="openVoucherModal(${doc.id}, '${doc.id_voucher || ''}')" title="Actualizar ID Voucher"><i class="fas fa-receipt"></i></button>
                                </div>
                            </td>
                            <td>${tipoDteBadge}</td>
                            <td>${doc.folio || ''}</td>
                            <td>${doc.fecha_generacion || ''}</td>
                            <td>${doc.rut_receptor || ''}</td>
                            <td>${doc.razon_social_receptor || ''}</td>
                            <td>${montoFormateado}</td>
                            <td>${estadoSobreBadge}</td>
                            <td>${doc.id_sobre || ''}</td>
                            <td>${doc.trackid}</td>
                            <td class="voucher-cell">${doc.id_voucher}</td>
                        </tr>
                    `;
                    
                    tableBody.innerHTML += row;
                });
                
                // Reinicializar el simple-table si es necesario
                if (typeof initializeSimpleTable === 'function') {
                    initializeSimpleTable();
                }
                
                // Activar la funcionalidad de filtrado en cada columna
                document.querySelectorAll('.column-search').forEach(input => {
                    input.addEventListener('keyup', function() {
                        const column = this.getAttribute('data-column');
                        const value = this.value.toLowerCase();
                        
                        const rows = tableBody.querySelectorAll('tr');
                        rows.forEach(row => {
                            const cell = row.querySelector(`td:nth-child(${parseInt(column) + 1})`);
                            if (cell) {
                                const text = cell.textContent.toLowerCase();
                                if (text.includes(value)) {
                                    row.style.display = '';
                                } else {
                                    row.style.display = 'none';
                                }
                            }
                        });
                    });
                });
            }
        });
    </script>
    <script src="js/dashboard.js"></script>
<!-- Modal para actualizar ID Voucher -->
<div class="modal fade" id="voucherModal" tabindex="-1" aria-labelledby="voucherModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="voucherModalLabel">Actualizar ID Voucher</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="voucherForm">
                    <input type="hidden" id="dte_id" name="dte_id">
                    <div class="mb-3">
                        <label for="id_voucher" class="form-label">ID Voucher</label>
                        <input type="text" class="form-control" id="id_voucher" name="id_voucher" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="saveVoucherBtn">Guardar</button>
            </div>
        </div>
    </div>
</div>

<!-- Script para manejar la actualización del ID Voucher -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Función para abrir el modal y establecer el ID del DTE
        window.openVoucherModal = function(dteId, currentVoucher) {
            document.getElementById('dte_id').value = dteId;
            document.getElementById('id_voucher').value = currentVoucher || '';

            // Abrir el modal
            var voucherModal = new bootstrap.Modal(document.getElementById('voucherModal'));
            voucherModal.show();
        };

        // Manejar el evento de guardar
        document.getElementById('saveVoucherBtn').addEventListener('click', function() {
            const dteId = document.getElementById('dte_id').value;
            const idVoucher = document.getElementById('id_voucher').value;

            // Validar que el ID Voucher no esté vacío
            if (!idVoucher.trim()) {
                alert('Por favor, ingrese un ID de Voucher válido');
                return;
            }

            // Usar jQuery AJAX para enviar la solicitud
            $.ajax({
                url: 'actualizar_voucher.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    dte_id: dteId,
                    id_voucher: idVoucher
                },
                success: function(data) {
                    console.log('Respuesta recibida:', data);

                    if (data.success) {
                        // Actualizar la celda en la tabla sin recargar la página
                        const row = document.querySelector(`tr[data-dte-id="${dteId}"]`);
                        if (row) {
                            const voucherCell = row.querySelector('.voucher-cell');
                            if (voucherCell) {
                                voucherCell.textContent = idVoucher;
                                console.log('Celda actualizada con el nuevo ID Voucher:', idVoucher);
                            } else {
                                console.warn('No se encontró la celda para el ID Voucher');
                            }
                        } else {
                            console.warn('No se encontró la fila para el DTE ID:', dteId);
                        }

                        alert('ID Voucher actualizado correctamente');

                        // Cerrar el modal
                        var voucherModal = bootstrap.Modal.getInstance(document.getElementById('voucherModal'));
                        voucherModal.hide();
                    } else {
                        alert('Error al actualizar el ID Voucher: ' + data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error AJAX:', status, error);
                    console.error('Respuesta del servidor:', xhr.responseText);
                    alert('Error al procesar la solicitud. Consulta la consola para más detalles.');
                }
            });
        });
    });
</script>

</body>
</html>
